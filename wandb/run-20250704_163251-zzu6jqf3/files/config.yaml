_name_or_path:
    value: /data/hyeon/2.chekcpoints/LLMs/Qwen2.5-7B-Instruct
_wandb:
    value:
        cli_version: 0.21.0
        e:
            grjsvsp7wiozqg4s1tw97rqiy274majc:
                args:
                    - --local_rank=0
                    - --deepspeed
                    - deepspeed3.json
                    - --proctitle
                    - junkim100
                    - --model_name_or_path
                    - qwen2.5_7B
                    - --data_name
                    - /data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl
                    - --wb_project
                    - kullm-pro
                    - --wb_name
                    - OpenMathInstruct2_Qwen2.5_7B
                    - --output_name
                    - OpenMathInstruct2_Qwen2.5_7B
                    - --max_length
                    - "16384"
                    - --num_train_epochs
                    - "2"
                    - --per_device_train_batch_size
                    - "1"
                    - --per_device_eval_batch_size
                    - "1"
                    - --gradient_accumulation_steps
                    - "1"
                    - --save_only_model
                    - --learning_rate
                    - "1e-5"
                    - --weight_decay
                    - "0."
                    - --warmup_ratio
                    - "0."
                    - --lr_scheduler_type
                    - cosine
                    - --bf16
                    - "True"
                    - --tf32
                    - "True"
                    - --gradient_checkpointing
                    - "True"
                    - --logging_steps
                    - "1"
                codePath: train.py
                codePathLocal: train.py
                cpu_count: 64
                cpu_count_logical: 128
                cudaVersion: "12.3"
                disk:
                    /:
                        total: "1964618686464"
                        used: "190906621952"
                email: <EMAIL>
                executable: /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10
                gpu: NVIDIA RTX A6000
                gpu_count: 8
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-855eb063-38d7-c437-10c6-9868f029f701
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-d6dc840b-cb82-39f6-5ea2-b0a4f18bdf6e
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-47d47859-f7c9-3a5d-041f-91caee50ac0c
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-17d9dba6-e295-fcfa-0378-5fb370c94055
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-13aaa27d-9c08-63b9-a5f4-3459c3f0ffbe
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-8c83a879-8638-6ecc-eac2-281d5b59d505
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-3f97dcea-347c-cd62-b3b8-52705086bf09
                    - architecture: Ampere
                      cudaCores: 10752
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX A6000
                      uuid: GPU-e37570d8-ee58-0645-2da3-3234a6b9ef3f
                host: nlp-server-16
                memory:
                    total: "2151664984064"
                os: Linux-6.5.0-34-generic-x86_64-with-glibc2.35
                program: /data_x/junkim100/projects/finetune/train.py
                python: CPython 3.10.0
                root: /data_x/junkim100/projects/finetune
                startedAt: "2025-07-04T07:32:51.466695Z"
                writerId: grjsvsp7wiozqg4s1tw97rqiy274majc
        m:
            - "1": train/global_step
              "6":
                - 3
              "7": []
            - "2": '*'
              "5": 1
              "6":
                - 1
              "7": []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 11
                - 41
                - 49
                - 51
                - 71
            "2":
                - 1
                - 11
                - 41
                - 49
                - 51
                - 71
            "3":
                - 7
                - 13
                - 19
                - 62
                - 66
            "4": 3.10.0
            "5": 0.21.0
            "6": 4.52.3
            "9":
                "1": transformers_trainer
            "12": 0.21.0
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
add_cross_attention:
    value: false
architectures:
    value:
        - Qwen2ForCausalLM
attention_dropout:
    value: 0
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
bad_words_ids:
    value: null
batch_eval_metrics:
    value: false
begin_suppress_tokens:
    value: null
bf16:
    value: true
bf16_full_eval:
    value: false
bos_token_id:
    value: 151643
cache_dir:
    value: null
chunk_size_feed_forward:
    value: 0
cross_attention_hidden_size:
    value: null
data_seed:
    value: null
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 0
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: true
dataloader_prefetch_factor:
    value: null
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
decoder_start_token_id:
    value: null
deepspeed:
    value: deepspeed3.json
disable_tqdm:
    value: false
diversity_penalty:
    value: 0
do_eval:
    value: false
do_predict:
    value: false
do_sample:
    value: false
do_train:
    value: false
early_stopping:
    value: false
encoder_no_repeat_ngram_size:
    value: 0
eos_token_id:
    value: 151645
eval_accumulation_steps:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_on_start:
    value: false
eval_steps:
    value: null
eval_strategy:
    value: "no"
eval_use_gather_object:
    value: false
exponential_decay_length_penalty:
    value: null
finetuning_task:
    value: null
forced_bos_token_id:
    value: null
forced_eos_token_id:
    value: null
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
gradient_accumulation_steps:
    value: 1
gradient_checkpointing:
    value: true
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: null
group_by_length:
    value: false
half_precision_backend:
    value: auto
hidden_act:
    value: silu
hidden_size:
    value: 3584
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
id2label:
    value:
        "0": LABEL_0
        "1": LABEL_1
ignore_data_skip:
    value: false
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
initializer_range:
    value: 0.02
intermediate_size:
    value: 18944
is_decoder:
    value: false
is_encoder_decoder:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
label2id:
    value:
        LABEL_0: 0
        LABEL_1: 1
learning_rate:
    value: 1e-05
length_column_name:
    value: length
length_penalty:
    value: 1
load_best_model_at_end:
    value: false
local_rank:
    value: 0
log_level:
    value: passive
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: trainer_output/runs/Jul04_16-31-34_nlp-server-16
logging_first_step:
    value: false
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 1
logging_strategy:
    value: steps
lr_scheduler_type:
    value: cosine
max_grad_norm:
    value: 0.3
max_length:
    value: 20
max_position_embeddings:
    value: 32768
max_steps:
    value: -1
max_window_layers:
    value: 28
metric_for_best_model:
    value: null
min_length:
    value: 0
model/num_parameters:
    value: 0
model_type:
    value: qwen2
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
no_repeat_ngram_size:
    value: 0
num_attention_heads:
    value: 28
num_beam_groups:
    value: 1
num_beams:
    value: 1
num_hidden_layers:
    value: 28
num_key_value_heads:
    value: 4
num_return_sequences:
    value: 1
num_train_epochs:
    value: 2
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
output_attentions:
    value: false
output_dir:
    value: ./output/qwen2.5_7B/OpenMathInstruct2_Qwen2.5_7B
output_hidden_states:
    value: false
output_scores:
    value: false
overwrite_output_dir:
    value: false
pad_token_id:
    value: null
past_index:
    value: -1
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
prefix:
    value: null
problem_type:
    value: null
proctitle:
    value: junkim100
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
ray_scope:
    value: last
remove_invalid_values:
    value: false
remove_unused_columns:
    value: true
repetition_penalty:
    value: 1
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
return_dict:
    value: true
return_dict_in_generate:
    value: false
rms_norm_eps:
    value: 1e-06
rope_scaling:
    value:
        factor: 4
        original_max_position_embeddings: 32768
        rope_type: yarn
        type: yarn
rope_theta:
    value: 1e+06
run_name:
    value: trainer_output
save_on_each_node:
    value: false
save_only_model:
    value: true
save_safetensors:
    value: true
save_steps:
    value: 500
save_strategy:
    value: steps
save_total_limit:
    value: null
seed:
    value: 42
sep_token_id:
    value: null
skip_memory_metrics:
    value: true
sliding_window:
    value: 131072
suppress_tokens:
    value: null
task_specific_params:
    value: null
temperature:
    value: 1
tf_legacy_loss:
    value: false
tf32:
    value: true
tie_encoder_decoder:
    value: false
tie_word_embeddings:
    value: false
tokenizer_class:
    value: null
top_k:
    value: 50
top_p:
    value: 1
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: float32
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
torchscript:
    value: false
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
transformers_version:
    value: 4.52.3
typical_p:
    value: 1
use_bfloat16:
    value: false
use_cache:
    value: true
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
use_sliding_window:
    value: false
vocab_size:
    value: 152064
warmup_ratio:
    value: 0
warmup_steps:
    value: 0
wb_name:
    value: OpenMathInstruct2_Qwen2.5_7B
wb_project:
    value: kullm-pro
weight_decay:
    value: 0
