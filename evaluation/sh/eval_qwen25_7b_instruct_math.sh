#!/bin/bash

# =============================================================================
# Mathematical Reasoning Evaluation Script for Qwen/Qwen2.5-7B-Instruct
# =============================================================================
# This script evaluates the Qwen/Qwen2.5-7B-Instruct model on mathematical 
# reasoning datasets using appropriate prompt types and evaluation parameters.
#
# Key Features:
# - Uses "qwen-boxed" prompt type (appropriate for general Qwen2.5 models)
# - Evaluates on: minerva_math, math, olympiadbench, aime24
# - Optimized parameters for mathematical reasoning
# - Comprehensive output logging and organization
# =============================================================================

set -ex

# =============================================================================
# Configuration Parameters
# =============================================================================

# Model Configuration
MODEL_NAME_OR_PATH="Qwen/Qwen2.5-7B-Instruct"
PROMPT_TYPE="qwen-boxed"  # Appropriate for general Qwen2.5 models (not math-specific)

# GPU Configuration - Adjust based on your setup
export CUDA_VISIBLE_DEVICES="0"

# Evaluation Parameters
SPLIT="test"
NUM_TEST_SAMPLE=-1  # -1 for full dataset, set positive number for subset
SEED=0

# Mathematical Reasoning Parameters
TEMPERATURE=0        # Greedy decoding for consistent mathematical reasoning
N_SAMPLING=1        # Single sample per problem for deterministic evaluation
TOP_P=1             # Must be 1 when temperature=0 (greedy sampling)
MAX_TOKENS=2048     # Sufficient for mathematical explanations

# Output Configuration
OUTPUT_DIR="${MODEL_NAME_OR_PATH}/math_eval_$(date +%Y%m%d_%H%M%S)"
SAVE_OUTPUTS=true
OVERWRITE=false     # Set to true to overwrite existing results

# =============================================================================
# Dataset Definitions
# =============================================================================

# Target mathematical datasets for evaluation
MATH_DATASETS="minerva_math,math,olympiadbench,aime24"

# =============================================================================
# Evaluation Function
# =============================================================================

run_evaluation() {
    local datasets=$1
    local num_shots=$2
    local additional_args=$3
    local description=$4
    
    echo "=========================================="
    echo "Evaluating: $description"
    echo "Datasets: $datasets"
    echo "Few-shot examples: $num_shots"
    echo "=========================================="
    
    TOKENIZERS_PARALLELISM=false \
    python3 -u math_eval.py \
        --model_name_or_path ${MODEL_NAME_OR_PATH} \
        --data_names ${datasets} \
        --output_dir ${OUTPUT_DIR} \
        --split ${SPLIT} \
        --prompt_type ${PROMPT_TYPE} \
        --num_test_sample ${NUM_TEST_SAMPLE} \
        --seed ${SEED} \
        --temperature ${TEMPERATURE} \
        --n_sampling ${N_SAMPLING} \
        --top_p ${TOP_P} \
        --max_tokens_per_call ${MAX_TOKENS} \
        --start 0 \
        --end -1 \
        --use_vllm \
        $([ "$SAVE_OUTPUTS" = true ] && echo "--save_outputs") \
        $([ "$OVERWRITE" = true ] && echo "--overwrite") \
        $([ "$num_shots" -gt 0 ] && echo "--num_shots $num_shots") \
        $additional_args
}

# =============================================================================
# Main Evaluation Execution
# =============================================================================

echo "Starting mathematical reasoning evaluation for ${MODEL_NAME_OR_PATH}"
echo "Prompt type: ${PROMPT_TYPE}"
echo "Output directory: ${OUTPUT_DIR}"
echo "GPU devices: ${CUDA_VISIBLE_DEVICES}"
echo ""

# Create output directory
mkdir -p "${OUTPUT_DIR}"

# Log configuration to file
cat > "${OUTPUT_DIR}/evaluation_config.txt" << EOF
Evaluation Configuration
========================
Model: ${MODEL_NAME_OR_PATH}
Prompt Type: ${PROMPT_TYPE}
Datasets: ${MATH_DATASETS}
Temperature: ${TEMPERATURE}
Sampling: ${N_SAMPLING}
Max Tokens: ${MAX_TOKENS}
Seed: ${SEED}
GPU Devices: ${CUDA_VISIBLE_DEVICES}
Timestamp: $(date)
EOF

# Run evaluation on target mathematical datasets
# Zero-shot evaluation (most challenging and standard for these datasets)
run_evaluation \
    "${MATH_DATASETS}" \
    0 \
    "" \
    "Mathematical Reasoning Datasets (Zero-shot)"

echo ""
echo "=========================================="
echo "Evaluation completed successfully!"
echo "Results saved to: ${OUTPUT_DIR}"
echo "=========================================="

# Optional: Display summary if results exist
if [ -d "${OUTPUT_DIR}" ]; then
    echo ""
    echo "Generated output files:"
    find "${OUTPUT_DIR}" -name "*.jsonl" -o -name "*_metrics.json" | head -10
    
    echo ""
    echo "To view detailed results, check the metrics files:"
    echo "find ${OUTPUT_DIR} -name '*_metrics.json' -exec cat {} \;"
fi
