#!/bin/bash

# =============================================================================
# Individual Dataset Evaluation for Qwen/Qwen2.5-7B-Instruct
# =============================================================================
# This script evaluates each mathematical dataset individually with optimized
# parameters for each dataset type. Provides detailed parameter explanations.
#
# Usage: ./eval_individual_datasets.sh [dataset_name]
# Available datasets: minerva_math, math, olympiadbench, aime24, all
# =============================================================================

set -ex

# =============================================================================
# Model and Basic Configuration
# =============================================================================

MODEL_NAME_OR_PATH="Qwen/Qwen2.5-7B-Instruct"
PROMPT_TYPE="qwen-boxed"  # Uses Qwen chat format with boxed answer requirement
export CUDA_VISIBLE_DEVICES="0"

# Base output directory
BASE_OUTPUT_DIR="${MODEL_NAME_OR_PATH}/individual_math_eval_$(date +%Y%m%d_%H%M%S)"

# =============================================================================
# Parameter Explanations and Configurations
# =============================================================================

# Mathematical reasoning parameters explained:
# - temperature=0: Greedy decoding for deterministic, consistent mathematical reasoning
# - n_sampling=1: Single sample per problem (standard for mathematical evaluation)
# - top_p=1: Must be 1 when temperature=0 (required by vLLM for greedy sampling)
# - max_tokens=2048: Sufficient for detailed mathematical explanations and solutions
# - seed=0: Fixed seed for reproducible results across runs

COMMON_PARAMS="
    --model_name_or_path ${MODEL_NAME_OR_PATH}
    --prompt_type ${PROMPT_TYPE}
    --split test
    --seed 0
    --temperature 0
    --n_sampling 1
    --top_p 1
    --max_tokens_per_call 2048
    --start 0
    --end -1
    --use_vllm
    --save_outputs
    --overwrite
"

# =============================================================================
# Dataset-Specific Evaluation Functions
# =============================================================================

evaluate_minerva_math() {
    echo "Evaluating MINERVA_MATH dataset..."
    echo "Dataset info: High-quality mathematical problems with detailed solutions"
    echo "Characteristics: Diverse difficulty levels, comprehensive mathematical topics"
    
    local output_dir="${BASE_OUTPUT_DIR}/minerva_math"
    mkdir -p "$output_dir"
    
    TOKENIZERS_PARALLELISM=false \
    python3 -u math_eval.py \
        --data_names "minerva_math" \
        --output_dir "$output_dir" \
        --num_test_sample -1 \
        $COMMON_PARAMS
    
    echo "MINERVA_MATH evaluation completed. Results in: $output_dir"
}

evaluate_math() {
    echo "Evaluating MATH dataset..."
    echo "Dataset info: Competition mathematics problems from AMC, AIME, USAMO, etc."
    echo "Characteristics: High difficulty, requires advanced mathematical reasoning"
    
    local output_dir="${BASE_OUTPUT_DIR}/math"
    mkdir -p "$output_dir"
    
    TOKENIZERS_PARALLELISM=false \
    python3 -u math_eval.py \
        --data_names "math" \
        --output_dir "$output_dir" \
        --num_test_sample -1 \
        $COMMON_PARAMS
    
    echo "MATH evaluation completed. Results in: $output_dir"
}

evaluate_olympiadbench() {
    echo "Evaluating OLYMPIADBENCH dataset..."
    echo "Dataset info: Mathematical olympiad problems"
    echo "Characteristics: Extremely challenging, requires creative problem-solving"
    
    local output_dir="${BASE_OUTPUT_DIR}/olympiadbench"
    mkdir -p "$output_dir"
    
    TOKENIZERS_PARALLELISM=false \
    python3 -u math_eval.py \
        --data_names "olympiadbench" \
        --output_dir "$output_dir" \
        --num_test_sample -1 \
        $COMMON_PARAMS
    
    echo "OLYMPIADBENCH evaluation completed. Results in: $output_dir"
}

evaluate_aime24() {
    echo "Evaluating AIME24 dataset..."
    echo "Dataset info: 2024 American Invitational Mathematics Examination problems"
    echo "Characteristics: Competition-level, integer answers from 0-999"
    
    local output_dir="${BASE_OUTPUT_DIR}/aime24"
    mkdir -p "$output_dir"
    
    TOKENIZERS_PARALLELISM=false \
    python3 -u math_eval.py \
        --data_names "aime24" \
        --output_dir "$output_dir" \
        --num_test_sample -1 \
        $COMMON_PARAMS
    
    echo "AIME24 evaluation completed. Results in: $output_dir"
}

# =============================================================================
# Results Analysis Function
# =============================================================================

analyze_results() {
    echo ""
    echo "=========================================="
    echo "EVALUATION RESULTS SUMMARY"
    echo "=========================================="
    
    if [ -d "$BASE_OUTPUT_DIR" ]; then
        for dataset_dir in "$BASE_OUTPUT_DIR"/*; do
            if [ -d "$dataset_dir" ]; then
                dataset_name=$(basename "$dataset_dir")
                echo ""
                echo "Dataset: $dataset_name"
                echo "----------------------------------------"
                
                # Find and display metrics
                metrics_file=$(find "$dataset_dir" -name "*_metrics.json" | head -1)
                if [ -f "$metrics_file" ]; then
                    echo "Metrics file: $metrics_file"
                    # Extract accuracy if available
                    if command -v jq >/dev/null 2>&1; then
                        acc=$(jq -r '.acc // "N/A"' "$metrics_file" 2>/dev/null || echo "N/A")
                        echo "Accuracy: $acc%"
                    else
                        echo "Install 'jq' for detailed metrics parsing"
                    fi
                else
                    echo "No metrics file found"
                fi
                
                # List output files
                echo "Output files:"
                find "$dataset_dir" -name "*.jsonl" -exec basename {} \; | head -3
            fi
        done
        
        echo ""
        echo "Complete results directory: $BASE_OUTPUT_DIR"
        echo ""
        echo "To view detailed metrics:"
        echo "  find $BASE_OUTPUT_DIR -name '*_metrics.json' -exec cat {} \;"
        echo ""
        echo "To analyze specific dataset results:"
        echo "  cat $BASE_OUTPUT_DIR/DATASET_NAME/*_metrics.json"
    fi
}

# =============================================================================
# Main Execution Logic
# =============================================================================

DATASET=${1:-"all"}

echo "Individual Mathematical Dataset Evaluation"
echo "=========================================="
echo "Model: $MODEL_NAME_OR_PATH"
echo "Prompt Type: $PROMPT_TYPE"
echo "Target Dataset(s): $DATASET"
echo "Base Output Directory: $BASE_OUTPUT_DIR"
echo ""

# Create base output directory
mkdir -p "$BASE_OUTPUT_DIR"

# Save configuration
cat > "$BASE_OUTPUT_DIR/evaluation_info.txt" << EOF
Individual Mathematical Dataset Evaluation
==========================================
Model: $MODEL_NAME_OR_PATH
Prompt Type: $PROMPT_TYPE
Evaluation Date: $(date)
GPU Devices: $CUDA_VISIBLE_DEVICES

Parameter Explanations:
- temperature=0: Greedy decoding for consistent mathematical reasoning
- n_sampling=1: Single sample per problem (standard evaluation)
- top_p=1: Required for greedy sampling with vLLM
- max_tokens=2048: Sufficient for detailed mathematical solutions
- seed=0: Fixed seed for reproducible results

Dataset Information:
- minerva_math: High-quality mathematical problems with detailed solutions
- math: Competition mathematics (AMC, AIME, USAMO) - high difficulty
- olympiadbench: Mathematical olympiad problems - extremely challenging
- aime24: 2024 AIME problems - competition level, integer answers 0-999
EOF

# Execute evaluation based on dataset selection
case $DATASET in
    "minerva_math")
        evaluate_minerva_math
        ;;
    "math")
        evaluate_math
        ;;
    "olympiadbench")
        evaluate_olympiadbench
        ;;
    "aime24")
        evaluate_aime24
        ;;
    "all")
        echo "Evaluating all target datasets..."
        evaluate_minerva_math
        evaluate_math
        evaluate_olympiadbench
        evaluate_aime24
        ;;
    *)
        echo "Unknown dataset: $DATASET"
        echo "Available options: minerva_math, math, olympiadbench, aime24, all"
        exit 1
        ;;
esac

# Analyze and display results
analyze_results

echo ""
echo "Individual dataset evaluation completed successfully!"
echo "Check $BASE_OUTPUT_DIR for detailed results."
