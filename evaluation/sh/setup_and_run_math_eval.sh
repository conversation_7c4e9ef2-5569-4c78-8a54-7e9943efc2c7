#!/bin/bash

# =============================================================================
# Setup and Run Mathematical Evaluation for Qwen/Qwen2.5-7B-Instruct
# =============================================================================
# This utility script sets up the evaluation environment and provides easy
# access to all mathematical evaluation options.
#
# Usage: ./setup_and_run_math_eval.sh [option]
# Options: setup, quick, full, individual, custom, help
# =============================================================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EVAL_DIR="$(dirname "$SCRIPT_DIR")"

# =============================================================================
# Setup Function
# =============================================================================

setup_evaluation() {
    echo "Setting up mathematical evaluation environment..."
    
    # Make all scripts executable
    chmod +x "$SCRIPT_DIR"/*.sh
    
    # Check if we're in the right directory
    if [ ! -f "$EVAL_DIR/math_eval.py" ]; then
        echo "Error: math_eval.py not found. Please run this script from the evaluation directory."
        exit 1
    fi
    
    # Check Python dependencies
    echo "Checking Python dependencies..."
    python3 -c "import vllm, transformers, torch" 2>/dev/null || {
        echo "Warning: Some required packages may be missing."
        echo "Please ensure you have: vllm, transformers, torch"
        echo "Install with: pip install vllm transformers torch"
    }
    
    # Check CUDA availability
    if command -v nvidia-smi >/dev/null 2>&1; then
        echo "CUDA devices available:"
        nvidia-smi --query-gpu=index,name,memory.total --format=csv,noheader,nounits
    else
        echo "Warning: nvidia-smi not found. GPU evaluation may not work."
    fi
    
    echo "Setup completed successfully!"
    echo ""
    show_usage
}

# =============================================================================
# Usage Information
# =============================================================================

show_usage() {
    cat << EOF
Mathematical Evaluation Scripts for Qwen/Qwen2.5-7B-Instruct
============================================================

Available evaluation options:

1. Quick Evaluation (recommended for testing)
   ./eval_qwen25_comprehensive_math.sh quick
   - Evaluates 50 samples per dataset
   - Fast execution for testing setup

2. Full Evaluation (comprehensive assessment)
   ./eval_qwen25_comprehensive_math.sh full
   - Evaluates complete datasets
   - Extended dataset collection
   - Takes longer but provides complete results

3. Individual Dataset Evaluation (detailed analysis)
   ./eval_individual_datasets.sh [dataset_name]
   - Available datasets: minerva_math, math, olympiadbench, aime24, all
   - Optimized parameters for each dataset
   - Detailed result analysis

4. Custom Evaluation (interactive configuration)
   ./eval_qwen25_comprehensive_math.sh custom
   - Interactive setup for custom configurations
   - Choose datasets, sample sizes, parameters

5. Simple Focused Evaluation (your specific requirements)
   ./eval_qwen25_7b_instruct_math.sh
   - Evaluates exactly: minerva_math, math, olympiadbench, aime24
   - Zero-shot evaluation with optimal parameters

Key Features:
- Uses 'qwen-boxed' prompt type (appropriate for Qwen2.5-7B-Instruct)
- Temperature=0 for deterministic mathematical reasoning
- Comprehensive output logging and organization
- GPU acceleration with vLLM
- Detailed parameter explanations

Examples:
  # Quick test run
  ./eval_qwen25_comprehensive_math.sh quick
  
  # Evaluate specific dataset
  ./eval_individual_datasets.sh math
  
  # Full evaluation of target datasets
  ./eval_qwen25_7b_instruct_math.sh
  
  # Evaluate all datasets individually
  ./eval_individual_datasets.sh all

Output Structure:
- Results saved in model-specific directories
- JSON metrics files for quantitative analysis
- JSONL files with detailed predictions
- Configuration logs for reproducibility

For more information, check the comments in each script file.
EOF
}

# =============================================================================
# Quick Start Functions
# =============================================================================

run_quick_test() {
    echo "Running quick mathematical evaluation test..."
    echo "This will evaluate 50 samples per dataset for testing."
    echo ""
    read -p "Continue? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        cd "$EVAL_DIR"
        bash sh/eval_qwen25_comprehensive_math.sh quick
    else
        echo "Quick test cancelled."
    fi
}

run_full_evaluation() {
    echo "Running full mathematical evaluation..."
    echo "This will evaluate complete datasets and may take several hours."
    echo ""
    read -p "Continue? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        cd "$EVAL_DIR"
        bash sh/eval_qwen25_comprehensive_math.sh full
    else
        echo "Full evaluation cancelled."
    fi
}

run_target_evaluation() {
    echo "Running evaluation on your target datasets..."
    echo "Datasets: minerva_math, math, olympiadbench, aime24"
    echo ""
    read -p "Continue? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        cd "$EVAL_DIR"
        bash sh/eval_qwen25_7b_instruct_math.sh
    else
        echo "Target evaluation cancelled."
    fi
}

# =============================================================================
# Main Execution
# =============================================================================

OPTION=${1:-"help"}

echo "Mathematical Evaluation Setup for Qwen/Qwen2.5-7B-Instruct"
echo "==========================================================="
echo ""

case $OPTION in
    "setup")
        setup_evaluation
        ;;
    "quick")
        run_quick_test
        ;;
    "full")
        run_full_evaluation
        ;;
    "target")
        run_target_evaluation
        ;;
    "individual")
        echo "Individual dataset evaluation options:"
        echo "  ./eval_individual_datasets.sh minerva_math"
        echo "  ./eval_individual_datasets.sh math"
        echo "  ./eval_individual_datasets.sh olympiadbench"
        echo "  ./eval_individual_datasets.sh aime24"
        echo "  ./eval_individual_datasets.sh all"
        echo ""
        read -p "Enter dataset name (or 'all'): " dataset
        cd "$EVAL_DIR"
        bash sh/eval_individual_datasets.sh "$dataset"
        ;;
    "custom")
        cd "$EVAL_DIR"
        bash sh/eval_qwen25_comprehensive_math.sh custom
        ;;
    "help"|*)
        show_usage
        echo ""
        echo "Run './setup_and_run_math_eval.sh setup' to initialize the environment."
        ;;
esac

echo ""
echo "For questions or issues, check the script comments or documentation."
