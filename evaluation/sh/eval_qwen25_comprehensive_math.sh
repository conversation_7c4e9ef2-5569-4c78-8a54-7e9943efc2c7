#!/bin/bash

# =============================================================================
# Comprehensive Mathematical Evaluation for Qwen/Qwen2.5-7B-Instruct
# =============================================================================
# This script provides comprehensive evaluation options for mathematical 
# reasoning with different configurations and dataset combinations.
#
# Usage:
#   ./eval_qwen25_comprehensive_math.sh [quick|full|custom]
#   
# Modes:
#   quick  - Fast evaluation on subset of problems
#   full   - Complete evaluation on all datasets  
#   custom - Interactive mode for custom configuration
# =============================================================================

set -e  # Exit on error but allow user interaction

# =============================================================================
# Default Configuration
# =============================================================================

MODEL_NAME_OR_PATH="Qwen/Qwen2.5-7B-Instruct"
PROMPT_TYPE="qwen-boxed"
export CUDA_VISIBLE_DEVICES="0"

# Evaluation mode from command line argument
EVAL_MODE=${1:-"quick"}

# =============================================================================
# Dataset Configurations
# =============================================================================

# Core mathematical reasoning datasets (your requested ones)
CORE_MATH_DATASETS="minerva_math,math,olympiadbench,aime24"

# Extended mathematical datasets for comprehensive evaluation
EXTENDED_MATH_DATASETS="gsm8k,math,svamp,minerva_math,olympiadbench,aime24,amc23"

# Competition and advanced datasets
COMPETITION_DATASETS="olympiadbench,aime24,amc23"

# =============================================================================
# Configuration Functions
# =============================================================================

setup_quick_eval() {
    echo "Setting up QUICK evaluation mode..."
    NUM_TEST_SAMPLE=50      # Subset for quick testing
    TEMPERATURE=0
    N_SAMPLING=1
    DATASETS=$CORE_MATH_DATASETS
    OUTPUT_SUFFIX="quick"
}

setup_full_eval() {
    echo "Setting up FULL evaluation mode..."
    NUM_TEST_SAMPLE=-1      # Full datasets
    TEMPERATURE=0
    N_SAMPLING=1
    DATASETS=$EXTENDED_MATH_DATASETS
    OUTPUT_SUFFIX="full"
}

setup_custom_eval() {
    echo "Setting up CUSTOM evaluation mode..."
    echo "Please configure your evaluation:"
    
    # Dataset selection
    echo ""
    echo "Available dataset options:"
    echo "1) Core math datasets (minerva_math,math,olympiadbench,aime24)"
    echo "2) Extended math datasets (includes GSM8K, SVAMP, etc.)"
    echo "3) Competition datasets only (olympiadbench,aime24,amc23)"
    echo "4) Custom dataset list"
    
    read -p "Select dataset option (1-4): " dataset_choice
    case $dataset_choice in
        1) DATASETS=$CORE_MATH_DATASETS ;;
        2) DATASETS=$EXTENDED_MATH_DATASETS ;;
        3) DATASETS=$COMPETITION_DATASETS ;;
        4) read -p "Enter comma-separated dataset names: " DATASETS ;;
        *) echo "Invalid choice, using core datasets"; DATASETS=$CORE_MATH_DATASETS ;;
    esac
    
    # Sample size
    read -p "Number of test samples (-1 for full, or positive number): " NUM_TEST_SAMPLE
    
    # Temperature setting
    echo ""
    echo "Temperature options:"
    echo "0 - Greedy decoding (deterministic, recommended for math)"
    echo "0.1 - Very low randomness"
    echo "0.3 - Low randomness"
    read -p "Enter temperature (0-1): " TEMPERATURE
    
    # Sampling
    read -p "Number of samples per problem (1 recommended): " N_SAMPLING
    
    OUTPUT_SUFFIX="custom"
}

# =============================================================================
# Evaluation Execution Function
# =============================================================================

run_math_evaluation() {
    local datasets=$1
    local description=$2
    local additional_args=$3
    
    echo ""
    echo "=========================================="
    echo "Running: $description"
    echo "Datasets: $datasets"
    echo "Samples per dataset: $NUM_TEST_SAMPLE"
    echo "Temperature: $TEMPERATURE"
    echo "=========================================="
    
    TOKENIZERS_PARALLELISM=false \
    python3 -u math_eval.py \
        --model_name_or_path ${MODEL_NAME_OR_PATH} \
        --data_names ${datasets} \
        --output_dir ${OUTPUT_DIR} \
        --split test \
        --prompt_type ${PROMPT_TYPE} \
        --num_test_sample ${NUM_TEST_SAMPLE} \
        --seed 0 \
        --temperature ${TEMPERATURE} \
        --n_sampling ${N_SAMPLING} \
        --top_p $([ "$TEMPERATURE" = "0" ] && echo "1" || echo "0.9") \
        --max_tokens_per_call 2048 \
        --start 0 \
        --end -1 \
        --use_vllm \
        --save_outputs \
        $additional_args
}

# =============================================================================
# Main Execution
# =============================================================================

echo "Mathematical Reasoning Evaluation for Qwen/Qwen2.5-7B-Instruct"
echo "================================================================"
echo "Mode: $EVAL_MODE"
echo ""

# Setup based on mode
case $EVAL_MODE in
    "quick")
        setup_quick_eval
        ;;
    "full")
        setup_full_eval
        ;;
    "custom")
        setup_custom_eval
        ;;
    *)
        echo "Unknown mode: $EVAL_MODE"
        echo "Usage: $0 [quick|full|custom]"
        exit 1
        ;;
esac

# Create output directory with timestamp and mode
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_DIR="${MODEL_NAME_OR_PATH}/math_eval_${OUTPUT_SUFFIX}_${TIMESTAMP}"
mkdir -p "${OUTPUT_DIR}"

# Log configuration
cat > "${OUTPUT_DIR}/config.txt" << EOF
Mathematical Reasoning Evaluation Configuration
==============================================
Model: ${MODEL_NAME_OR_PATH}
Prompt Type: ${PROMPT_TYPE}
Evaluation Mode: ${EVAL_MODE}
Datasets: ${DATASETS}
Test Samples: ${NUM_TEST_SAMPLE}
Temperature: ${TEMPERATURE}
N Sampling: ${N_SAMPLING}
GPU Devices: ${CUDA_VISIBLE_DEVICES}
Timestamp: $(date)
EOF

echo "Configuration saved to: ${OUTPUT_DIR}/config.txt"
echo "Starting evaluation..."

# Run the evaluation
run_math_evaluation \
    "${DATASETS}" \
    "Mathematical Reasoning Evaluation (${EVAL_MODE} mode)" \
    ""

# =============================================================================
# Results Summary
# =============================================================================

echo ""
echo "=========================================="
echo "Evaluation completed!"
echo "=========================================="
echo "Results directory: ${OUTPUT_DIR}"
echo ""

# Display results summary if available
if [ -d "${OUTPUT_DIR}" ]; then
    echo "Generated files:"
    find "${OUTPUT_DIR}" -name "*.jsonl" -o -name "*_metrics.json" | sort
    
    echo ""
    echo "Metrics files:"
    find "${OUTPUT_DIR}" -name "*_metrics.json" -exec echo "  {}" \;
    
    echo ""
    echo "To view results:"
    echo "  cat ${OUTPUT_DIR}/*_metrics.json"
    echo ""
    echo "To analyze specific dataset:"
    echo "  find ${OUTPUT_DIR} -name '*DATASET_NAME*_metrics.json' -exec cat {} \;"
fi

echo "Evaluation script completed successfully!"
