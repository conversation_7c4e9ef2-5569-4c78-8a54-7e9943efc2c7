[2025-07-04 16:31:22,222] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:25,335] [WARNING] [runner.py:215:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=0,1,2,3,4,5,6,7 but ignoring it because one or several of --include/--exclude/--num_gpus/--num_nodes cl args were used. If you want to use CUDA_VISIBLE_DEVICES don't pass any of these arguments to deepspeed.
[2025-07-04 16:31:25,335] [INFO] [runner.py:605:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbMCwgMSwgMiwgMywgNCwgNSwgNiwgN119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train.py --deepspeed deepspeed3.json --proctitle junkim100 --model_name_or_path qwen2.5_7B --data_name /data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl --wb_project kullm-pro --wb_name OpenMathInstruct2_Qwen2.5_7B --output_name OpenMathInstruct2_Qwen2.5_7B --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-5 --weight_decay 0. --warmup_ratio 0. --lr_scheduler_type cosine --bf16 True --tf32 True --gradient_checkpointing True --logging_steps 1
[2025-07-04 16:31:27,326] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:30,518] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]}
[2025-07-04 16:31:30,518] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=8, node_rank=0
[2025-07-04 16:31:30,518] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]})
[2025-07-04 16:31:30,518] [INFO] [launch.py:164:main] dist_world_size=8
[2025-07-04 16:31:30,518] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
[2025-07-04 16:31:30,520] [INFO] [launch.py:256:main] process 3760839 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=0', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,521] [INFO] [launch.py:256:main] process 3760840 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=1', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,522] [INFO] [launch.py:256:main] process 3760841 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=2', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,523] [INFO] [launch.py:256:main] process 3760842 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=3', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,523] [INFO] [launch.py:256:main] process 3760843 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=4', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,524] [INFO] [launch.py:256:main] process 3760844 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=5', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,525] [INFO] [launch.py:256:main] process 3760845 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=6', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:30,525] [INFO] [launch.py:256:main] process 3760846 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=7', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'OpenMathInstruct2_Qwen2.5_7B', '--output_name', 'OpenMathInstruct2_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 16:31:35,022] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,433] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,476] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,574] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,682] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,682] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,685] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:35,686] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 16:31:36,619] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:36,917] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:36,938] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:36,938] [INFO] [comm.py:700:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-07-04 16:31:37,051] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:37,223] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:37,233] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:37,270] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-04 16:31:37,310] [INFO] [comm.py:669:init_distributed] cdb=None
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=0,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=7,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=6,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=1,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=3,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=5,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=4,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=2,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul04_16-31-34_nlp-server-16,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=OpenMathInstruct2_Qwen2.5_7B,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-07-04 16:31:38,717] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,795] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,796] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,800] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,811] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,812] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,827] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:38,842] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-04 16:31:55,236] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 339, num_elems = 7.62B

Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.66s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.66s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.66s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.66s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.66s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.65s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:21,  3.65s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:03<00:23,  3.86s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.37s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.37s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.37s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.37s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.37s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.38s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:06<00:16,  3.37s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [00:07<00:17,  3.48s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.43s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [00:10<00:13,  3.48s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.43s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [00:13<00:10,  3.46s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.39s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [00:17<00:06,  3.41s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [00:19<00:03,  3.02s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.52s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.98s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.49s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [00:20<00:00,  2.99s/it]
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:min_token_length: 113
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:min_token_length: 113
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:min_token_length: 113
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:min_token_length: 113
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:min_token_length: 113
WARNING:root:Data Prepared...
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:min_token_length: 113
WARNING:root:min_token_length: 113
WARNING:root:max_token_length: 1395
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 406.254851228978
WARNING:root:min_token_length: 113
WARNING:root:max_token_length: 1395
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:257: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=OpenMathInstruct2_Qwen2.5_7B; so disabling `WANDB_LOG_MODEL`
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
Parameter Offload: Total persistent parameters: 333312 in 141 params
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[2025-07-04 16:32:50,538] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,540] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,541] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,542] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,543] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,544] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,544] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-04 16:32:50,776] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
wandb: Currently logged in as: junkim100 (junkim) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /data_x/junkim100/projects/finetune/wandb/run-20250704_163251-zzu6jqf3
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run trainer_output
wandb: ⭐️ View project at https://wandb.ai/junkim/kullm-pro
wandb: 🚀 View run at https://wandb.ai/junkim/kullm-pro/runs/zzu6jqf3

  0%|          | 0/194 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.

  1%|          | 1/194 [00:37<2:02:00, 37.93s/it]
                                                 
{'loss': 0.5571, 'grad_norm': 9.651378631591797, 'learning_rate': 0.0, 'epoch': 0.01}

  1%|          | 1/194 [00:37<2:02:00, 37.93s/it]
  1%|          | 2/194 [01:07<1:45:24, 32.94s/it]
                                                 
{'loss': 0.7297, 'grad_norm': 18.937631607055664, 'learning_rate': 1e-05, 'epoch': 0.02}

  1%|          | 2/194 [01:07<1:45:24, 32.94s/it]
  2%|▏         | 3/194 [01:36<1:39:23, 31.22s/it]
                                                 
{'loss': 0.9286, 'grad_norm': 24.013242721557617, 'learning_rate': 1e-05, 'epoch': 0.03}

  2%|▏         | 3/194 [01:36<1:39:23, 31.22s/it]
  2%|▏         | 4/194 [02:05<1:36:25, 30.45s/it]
                                                 
{'loss': 0.5052, 'grad_norm': 7.9963459968566895, 'learning_rate': 9.947916666666667e-06, 'epoch': 0.04}

  2%|▏         | 4/194 [02:05<1:36:25, 30.45s/it]
  3%|▎         | 5/194 [02:35<1:34:38, 30.04s/it]
                                                 
{'loss': 0.5543, 'grad_norm': 6.779991626739502, 'learning_rate': 9.895833333333334e-06, 'epoch': 0.05}

  3%|▎         | 5/194 [02:35<1:34:38, 30.04s/it]
  3%|▎         | 6/194 [03:04<1:33:34, 29.86s/it]
                                                 
{'loss': 0.323, 'grad_norm': 4.2932353019714355, 'learning_rate': 9.84375e-06, 'epoch': 0.06}

  3%|▎         | 6/194 [03:04<1:33:34, 29.86s/it]
  4%|▎         | 7/194 [03:33<1:32:01, 29.53s/it]
                                                 
{'loss': 0.3251, 'grad_norm': 4.917591571807861, 'learning_rate': 9.791666666666666e-06, 'epoch': 0.07}

  4%|▎         | 7/194 [03:33<1:32:01, 29.53s/it]
  4%|▍         | 8/194 [04:02<1:31:16, 29.44s/it]
                                                 
{'loss': 0.2785, 'grad_norm': 4.323558330535889, 'learning_rate': 9.739583333333334e-06, 'epoch': 0.08}

  4%|▍         | 8/194 [04:02<1:31:16, 29.44s/it]
  5%|▍         | 9/194 [04:31<1:30:35, 29.38s/it]
                                                 
{'loss': 0.343, 'grad_norm': 4.550070762634277, 'learning_rate': 9.6875e-06, 'epoch': 0.09}

  5%|▍         | 9/194 [04:31<1:30:35, 29.38s/it]
  5%|▌         | 10/194 [05:00<1:29:34, 29.21s/it]
                                                  
{'loss': 0.3184, 'grad_norm': 5.288993835449219, 'learning_rate': 9.635416666666668e-06, 'epoch': 0.1}

  5%|▌         | 10/194 [05:00<1:29:34, 29.21s/it]
  6%|▌         | 11/194 [05:30<1:29:32, 29.36s/it]
                                                  
{'loss': 0.3162, 'grad_norm': 4.786186695098877, 'learning_rate': 9.583333333333335e-06, 'epoch': 0.11}

  6%|▌         | 11/194 [05:30<1:29:32, 29.36s/it]
  6%|▌         | 12/194 [06:00<1:29:21, 29.46s/it]
                                                  
{'loss': 0.3868, 'grad_norm': 5.472080230712891, 'learning_rate': 9.531250000000001e-06, 'epoch': 0.12}

  6%|▌         | 12/194 [06:00<1:29:21, 29.46s/it]
  7%|▋         | 13/194 [06:29<1:28:58, 29.49s/it]
                                                  
{'loss': 0.2928, 'grad_norm': 3.4111804962158203, 'learning_rate': 9.479166666666667e-06, 'epoch': 0.13}

  7%|▋         | 13/194 [06:29<1:28:58, 29.49s/it]
  7%|▋         | 14/194 [06:59<1:28:23, 29.47s/it]
                                                  
{'loss': 0.3489, 'grad_norm': 4.304617881774902, 'learning_rate': 9.427083333333335e-06, 'epoch': 0.14}

  7%|▋         | 14/194 [06:59<1:28:23, 29.47s/it]
  8%|▊         | 15/194 [07:28<1:27:56, 29.48s/it]
                                                  
{'loss': 0.3288, 'grad_norm': 4.3566975593566895, 'learning_rate': 9.375000000000001e-06, 'epoch': 0.15}

  8%|▊         | 15/194 [07:28<1:27:56, 29.48s/it]
  8%|▊         | 16/194 [07:58<1:27:23, 29.46s/it]
                                                  
{'loss': 0.3139, 'grad_norm': 3.217667818069458, 'learning_rate': 9.322916666666667e-06, 'epoch': 0.16}

  8%|▊         | 16/194 [07:58<1:27:23, 29.46s/it]
  9%|▉         | 17/194 [08:27<1:26:51, 29.44s/it]
                                                  
{'loss': 0.2632, 'grad_norm': 3.165393590927124, 'learning_rate': 9.270833333333334e-06, 'epoch': 0.18}

  9%|▉         | 17/194 [08:27<1:26:51, 29.44s/it]
  9%|▉         | 18/194 [08:57<1:26:46, 29.58s/it]
                                                  
{'loss': 0.2918, 'grad_norm': 9.126531600952148, 'learning_rate': 9.21875e-06, 'epoch': 0.19}

  9%|▉         | 18/194 [08:57<1:26:46, 29.58s/it]
 10%|▉         | 19/194 [09:26<1:26:07, 29.53s/it]
                                                  
{'loss': 0.3287, 'grad_norm': 4.760901927947998, 'learning_rate': 9.166666666666666e-06, 'epoch': 0.2}

 10%|▉         | 19/194 [09:26<1:26:07, 29.53s/it]
 10%|█         | 20/194 [09:56<1:25:32, 29.50s/it]
                                                  
{'loss': 0.2784, 'grad_norm': 2.9158220291137695, 'learning_rate': 9.114583333333334e-06, 'epoch': 0.21}

 10%|█         | 20/194 [09:56<1:25:32, 29.50s/it]
 11%|█         | 21/194 [10:25<1:25:03, 29.50s/it]
                                                  
{'loss': 0.3035, 'grad_norm': 3.0372860431671143, 'learning_rate': 9.0625e-06, 'epoch': 0.22}

 11%|█         | 21/194 [10:25<1:25:03, 29.50s/it]
 11%|█▏        | 22/194 [10:55<1:24:41, 29.54s/it]
                                                  
{'loss': 0.2745, 'grad_norm': 3.6379125118255615, 'learning_rate': 9.010416666666668e-06, 'epoch': 0.23}

 11%|█▏        | 22/194 [10:55<1:24:41, 29.54s/it]
 12%|█▏        | 23/194 [11:25<1:24:17, 29.57s/it]
                                                  
{'loss': 0.297, 'grad_norm': 3.5722239017486572, 'learning_rate': 8.958333333333334e-06, 'epoch': 0.24}

 12%|█▏        | 23/194 [11:25<1:24:17, 29.57s/it]
 12%|█▏        | 24/194 [11:54<1:23:46, 29.57s/it]
                                                  
{'loss': 0.2415, 'grad_norm': 3.1367475986480713, 'learning_rate': 8.906250000000001e-06, 'epoch': 0.25}

 12%|█▏        | 24/194 [11:54<1:23:46, 29.57s/it]
 13%|█▎        | 25/194 [12:24<1:23:19, 29.58s/it]
                                                  
{'loss': 0.3686, 'grad_norm': 5.706437110900879, 'learning_rate': 8.854166666666667e-06, 'epoch': 0.26}

 13%|█▎        | 25/194 [12:24<1:23:19, 29.58s/it]
 13%|█▎        | 26/194 [12:53<1:22:38, 29.51s/it]
                                                  
{'loss': 0.251, 'grad_norm': 3.115445613861084, 'learning_rate': 8.802083333333335e-06, 'epoch': 0.27}

 13%|█▎        | 26/194 [12:53<1:22:38, 29.51s/it]
 14%|█▍        | 27/194 [13:23<1:22:13, 29.54s/it]
                                                  
{'loss': 0.2795, 'grad_norm': 3.4524166584014893, 'learning_rate': 8.750000000000001e-06, 'epoch': 0.28}

 14%|█▍        | 27/194 [13:23<1:22:13, 29.54s/it]
 14%|█▍        | 28/194 [13:52<1:21:50, 29.58s/it]
                                                  
{'loss': 0.3913, 'grad_norm': 4.6821393966674805, 'learning_rate': 8.697916666666667e-06, 'epoch': 0.29}

 14%|█▍        | 28/194 [13:52<1:21:50, 29.58s/it]
 15%|█▍        | 29/194 [14:22<1:21:10, 29.52s/it]
                                                  
{'loss': 0.2624, 'grad_norm': 3.598221778869629, 'learning_rate': 8.645833333333335e-06, 'epoch': 0.3}

 15%|█▍        | 29/194 [14:22<1:21:10, 29.52s/it]
 15%|█▌        | 30/194 [14:51<1:20:52, 29.59s/it]
                                                  
{'loss': 0.3522, 'grad_norm': 4.022395133972168, 'learning_rate': 8.59375e-06, 'epoch': 0.31}

 15%|█▌        | 30/194 [14:51<1:20:52, 29.59s/it]
 16%|█▌        | 31/194 [15:21<1:20:17, 29.56s/it]
                                                  
{'loss': 0.3377, 'grad_norm': 3.8087539672851562, 'learning_rate': 8.541666666666666e-06, 'epoch': 0.32}

 16%|█▌        | 31/194 [15:21<1:20:17, 29.56s/it]
 16%|█▋        | 32/194 [15:51<1:19:59, 29.63s/it]
                                                  
{'loss': 0.2825, 'grad_norm': 3.0766983032226562, 'learning_rate': 8.489583333333334e-06, 'epoch': 0.33}

 16%|█▋        | 32/194 [15:51<1:19:59, 29.63s/it]
 17%|█▋        | 33/194 [16:21<1:19:37, 29.68s/it]
                                                  
{'loss': 0.3546, 'grad_norm': 3.5880627632141113, 'learning_rate': 8.4375e-06, 'epoch': 0.34}

 17%|█▋        | 33/194 [16:21<1:19:37, 29.68s/it]
 18%|█▊        | 34/194 [16:50<1:19:07, 29.67s/it]
                                                  
{'loss': 0.2986, 'grad_norm': 3.8596715927124023, 'learning_rate': 8.385416666666668e-06, 'epoch': 0.35}

 18%|█▊        | 34/194 [16:50<1:19:07, 29.67s/it]
 18%|█▊        | 35/194 [17:20<1:18:44, 29.72s/it]
                                                  
{'loss': 0.3679, 'grad_norm': 3.667907476425171, 'learning_rate': 8.333333333333334e-06, 'epoch': 0.36}

 18%|█▊        | 35/194 [17:20<1:18:44, 29.72s/it]
 19%|█▊        | 36/194 [17:50<1:18:14, 29.71s/it]
                                                  
{'loss': 0.2857, 'grad_norm': 3.127920627593994, 'learning_rate': 8.281250000000001e-06, 'epoch': 0.37}

 19%|█▊        | 36/194 [17:50<1:18:14, 29.71s/it]
 19%|█▉        | 37/194 [18:19<1:17:41, 29.69s/it]
                                                  
{'loss': 0.2826, 'grad_norm': 3.5093777179718018, 'learning_rate': 8.229166666666667e-06, 'epoch': 0.38}

 19%|█▉        | 37/194 [18:19<1:17:41, 29.69s/it]
 20%|█▉        | 38/194 [18:49<1:17:05, 29.65s/it]
                                                  
{'loss': 0.2939, 'grad_norm': 3.846273422241211, 'learning_rate': 8.177083333333335e-06, 'epoch': 0.39}

 20%|█▉        | 38/194 [18:49<1:17:05, 29.65s/it]
 20%|██        | 39/194 [19:19<1:16:43, 29.70s/it]
                                                  
{'loss': 0.3916, 'grad_norm': 4.555237770080566, 'learning_rate': 8.125000000000001e-06, 'epoch': 0.4}

 20%|██        | 39/194 [19:19<1:16:43, 29.70s/it]
 21%|██        | 40/194 [19:49<1:16:18, 29.73s/it]
                                                  
{'loss': 0.4211, 'grad_norm': 3.702270746231079, 'learning_rate': 8.072916666666667e-06, 'epoch': 0.41}

 21%|██        | 40/194 [19:49<1:16:18, 29.73s/it]
 21%|██        | 41/194 [20:18<1:15:47, 29.72s/it]
                                                  
{'loss': 0.2851, 'grad_norm': 3.9061808586120605, 'learning_rate': 8.020833333333335e-06, 'epoch': 0.42}

 21%|██        | 41/194 [20:18<1:15:47, 29.72s/it]
 22%|██▏       | 42/194 [20:48<1:15:05, 29.64s/it]
                                                  
{'loss': 0.3114, 'grad_norm': 3.5542070865631104, 'learning_rate': 7.96875e-06, 'epoch': 0.43}

 22%|██▏       | 42/194 [20:48<1:15:05, 29.64s/it]
 22%|██▏       | 43/194 [21:18<1:14:45, 29.70s/it]
                                                  
{'loss': 0.2893, 'grad_norm': 4.069955348968506, 'learning_rate': 7.916666666666667e-06, 'epoch': 0.44}

 22%|██▏       | 43/194 [21:18<1:14:45, 29.70s/it]
 23%|██▎       | 44/194 [21:47<1:14:11, 29.68s/it]
                                                  
{'loss': 0.2628, 'grad_norm': 3.1353611946105957, 'learning_rate': 7.864583333333334e-06, 'epoch': 0.45}

 23%|██▎       | 44/194 [21:47<1:14:11, 29.68s/it]
 23%|██▎       | 45/194 [22:16<1:13:25, 29.56s/it]
                                                  
{'loss': 0.3477, 'grad_norm': 2.989607572555542, 'learning_rate': 7.8125e-06, 'epoch': 0.46}

 23%|██▎       | 45/194 [22:16<1:13:25, 29.56s/it]
 24%|██▎       | 46/194 [22:46<1:13:05, 29.63s/it]
                                                  
{'loss': 0.2723, 'grad_norm': 3.8081862926483154, 'learning_rate': 7.760416666666666e-06, 'epoch': 0.47}

 24%|██▎       | 46/194 [22:46<1:13:05, 29.63s/it]
 24%|██▍       | 47/194 [23:16<1:12:34, 29.62s/it]
                                                  
{'loss': 0.3451, 'grad_norm': 3.637516736984253, 'learning_rate': 7.708333333333334e-06, 'epoch': 0.48}

 24%|██▍       | 47/194 [23:16<1:12:34, 29.62s/it]
 25%|██▍       | 48/194 [23:45<1:12:03, 29.61s/it]
                                                  
{'loss': 0.2951, 'grad_norm': 2.9692041873931885, 'learning_rate': 7.656250000000001e-06, 'epoch': 0.49}

 25%|██▍       | 48/194 [23:45<1:12:03, 29.61s/it]
 25%|██▌       | 49/194 [24:15<1:11:36, 29.63s/it]
                                                  
{'loss': 0.3036, 'grad_norm': 3.4804248809814453, 'learning_rate': 7.6041666666666666e-06, 'epoch': 0.51}

 25%|██▌       | 49/194 [24:15<1:11:36, 29.63s/it]
 26%|██▌       | 50/194 [24:45<1:11:05, 29.62s/it]
                                                  
{'loss': 0.313, 'grad_norm': 2.966902256011963, 'learning_rate': 7.552083333333334e-06, 'epoch': 0.52}

 26%|██▌       | 50/194 [24:45<1:11:05, 29.62s/it]
 26%|██▋       | 51/194 [25:15<1:10:44, 29.68s/it]
                                                  
{'loss': 0.3317, 'grad_norm': 3.5058021545410156, 'learning_rate': 7.500000000000001e-06, 'epoch': 0.53}

 26%|██▋       | 51/194 [25:15<1:10:44, 29.68s/it]
 27%|██▋       | 52/194 [25:44<1:10:10, 29.65s/it]
                                                  
{'loss': 0.365, 'grad_norm': 3.022214651107788, 'learning_rate': 7.447916666666667e-06, 'epoch': 0.54}

 27%|██▋       | 52/194 [25:44<1:10:10, 29.65s/it]
 27%|██▋       | 53/194 [26:14<1:09:38, 29.64s/it]
                                                  
{'loss': 0.4266, 'grad_norm': 4.580804824829102, 'learning_rate': 7.395833333333335e-06, 'epoch': 0.55}

 27%|██▋       | 53/194 [26:14<1:09:38, 29.64s/it]
 28%|██▊       | 54/194 [26:43<1:09:01, 29.58s/it]
                                                  
{'loss': 0.3297, 'grad_norm': 3.399122476577759, 'learning_rate': 7.343750000000001e-06, 'epoch': 0.56}

 28%|██▊       | 54/194 [26:43<1:09:01, 29.58s/it]
 28%|██▊       | 55/194 [27:13<1:08:26, 29.55s/it]
                                                  
{'loss': 0.3144, 'grad_norm': 3.6575255393981934, 'learning_rate': 7.291666666666667e-06, 'epoch': 0.57}

 28%|██▊       | 55/194 [27:13<1:08:26, 29.55s/it]
 29%|██▉       | 56/194 [27:42<1:07:55, 29.54s/it]
                                                  
{'loss': 0.3156, 'grad_norm': 3.731503486633301, 'learning_rate': 7.239583333333334e-06, 'epoch': 0.58}

 29%|██▉       | 56/194 [27:42<1:07:55, 29.54s/it]
 29%|██▉       | 57/194 [28:12<1:07:28, 29.55s/it]
                                                  
{'loss': 0.2435, 'grad_norm': 2.6669325828552246, 'learning_rate': 7.1875e-06, 'epoch': 0.59}

 29%|██▉       | 57/194 [28:12<1:07:28, 29.55s/it]
 30%|██▉       | 58/194 [28:42<1:07:14, 29.66s/it]
                                                  
{'loss': 0.3115, 'grad_norm': 3.081352710723877, 'learning_rate': 7.135416666666667e-06, 'epoch': 0.6}

 30%|██▉       | 58/194 [28:42<1:07:14, 29.66s/it]
 30%|███       | 59/194 [29:11<1:06:43, 29.66s/it]
                                                  
{'loss': 0.2538, 'grad_norm': 2.4796090126037598, 'learning_rate': 7.083333333333335e-06, 'epoch': 0.61}

 30%|███       | 59/194 [29:11<1:06:43, 29.66s/it]
 31%|███       | 60/194 [29:41<1:06:10, 29.63s/it]
                                                  
{'loss': 0.3524, 'grad_norm': 3.490065574645996, 'learning_rate': 7.031250000000001e-06, 'epoch': 0.62}

 31%|███       | 60/194 [29:41<1:06:10, 29.63s/it]
 31%|███▏      | 61/194 [30:10<1:05:35, 29.59s/it]
                                                  
{'loss': 0.3624, 'grad_norm': 3.4640207290649414, 'learning_rate': 6.979166666666667e-06, 'epoch': 0.63}

 31%|███▏      | 61/194 [30:10<1:05:35, 29.59s/it]
 32%|███▏      | 62/194 [30:40<1:05:04, 29.58s/it]
                                                  
{'loss': 0.3468, 'grad_norm': 4.1353254318237305, 'learning_rate': 6.927083333333334e-06, 'epoch': 0.64}

 32%|███▏      | 62/194 [30:40<1:05:04, 29.58s/it]
 32%|███▏      | 63/194 [31:10<1:04:42, 29.64s/it]
                                                  
{'loss': 0.2205, 'grad_norm': 2.5157604217529297, 'learning_rate': 6.875e-06, 'epoch': 0.65}

 32%|███▏      | 63/194 [31:10<1:04:42, 29.64s/it]
 33%|███▎      | 64/194 [31:39<1:04:07, 29.60s/it]
                                                  
{'loss': 0.334, 'grad_norm': 4.004231929779053, 'learning_rate': 6.822916666666667e-06, 'epoch': 0.66}

 33%|███▎      | 64/194 [31:39<1:04:07, 29.60s/it]
 34%|███▎      | 65/194 [32:09<1:03:42, 29.63s/it]
                                                  
{'loss': 0.4227, 'grad_norm': 5.0167155265808105, 'learning_rate': 6.770833333333334e-06, 'epoch': 0.67}

 34%|███▎      | 65/194 [32:09<1:03:42, 29.63s/it]
 34%|███▍      | 66/194 [32:38<1:03:10, 29.61s/it]
                                                  
{'loss': 0.2379, 'grad_norm': 2.630155324935913, 'learning_rate': 6.718750000000001e-06, 'epoch': 0.68}

 34%|███▍      | 66/194 [32:38<1:03:10, 29.61s/it]
 35%|███▍      | 67/194 [33:08<1:02:47, 29.67s/it]
                                                  
{'loss': 0.3774, 'grad_norm': 4.210110187530518, 'learning_rate': 6.666666666666667e-06, 'epoch': 0.69}

 35%|███▍      | 67/194 [33:08<1:02:47, 29.67s/it]
 35%|███▌      | 68/194 [33:38<1:02:23, 29.71s/it]
                                                  
{'loss': 0.3112, 'grad_norm': 3.36454701423645, 'learning_rate': 6.614583333333334e-06, 'epoch': 0.7}

 35%|███▌      | 68/194 [33:38<1:02:23, 29.71s/it]
 36%|███▌      | 69/194 [34:08<1:01:48, 29.67s/it]
                                                  
{'loss': 0.2241, 'grad_norm': 2.5758142471313477, 'learning_rate': 6.5625e-06, 'epoch': 0.71}

 36%|███▌      | 69/194 [34:08<1:01:48, 29.67s/it]
 36%|███▌      | 70/194 [34:37<1:01:19, 29.67s/it]
                                                  
{'loss': 0.212, 'grad_norm': 2.892721652984619, 'learning_rate': 6.510416666666667e-06, 'epoch': 0.72}

 36%|███▌      | 70/194 [34:37<1:01:19, 29.67s/it]
 37%|███▋      | 71/194 [35:07<1:00:52, 29.70s/it]
                                                  
{'loss': 0.2906, 'grad_norm': 2.9735536575317383, 'learning_rate': 6.458333333333334e-06, 'epoch': 0.73}

 37%|███▋      | 71/194 [35:07<1:00:52, 29.70s/it]
 37%|███▋      | 72/194 [35:37<1:00:36, 29.80s/it]
                                                  
{'loss': 0.3535, 'grad_norm': 3.1902637481689453, 'learning_rate': 6.406250000000001e-06, 'epoch': 0.74}

 37%|███▋      | 72/194 [35:37<1:00:36, 29.80s/it]
 38%|███▊      | 73/194 [36:07<1:00:04, 29.79s/it]
                                                  
{'loss': 0.2875, 'grad_norm': 3.3945538997650146, 'learning_rate': 6.354166666666667e-06, 'epoch': 0.75}

 38%|███▊      | 73/194 [36:07<1:00:04, 29.79s/it]
 38%|███▊      | 74/194 [36:37<59:42, 29.85s/it]  
                                                
{'loss': 0.2471, 'grad_norm': 2.5234103202819824, 'learning_rate': 6.302083333333334e-06, 'epoch': 0.76}

 38%|███▊      | 74/194 [36:37<59:42, 29.85s/it]
 39%|███▊      | 75/194 [37:07<59:09, 29.82s/it]
                                                
{'loss': 0.2763, 'grad_norm': 3.159088611602783, 'learning_rate': 6.25e-06, 'epoch': 0.77}

 39%|███▊      | 75/194 [37:07<59:09, 29.82s/it]
 39%|███▉      | 76/194 [37:36<58:34, 29.78s/it]
                                                
{'loss': 0.2548, 'grad_norm': 3.1282875537872314, 'learning_rate': 6.197916666666667e-06, 'epoch': 0.78}

 39%|███▉      | 76/194 [37:36<58:34, 29.78s/it]
 40%|███▉      | 77/194 [38:06<57:57, 29.72s/it]
                                                
{'loss': 0.3461, 'grad_norm': 3.4531409740448, 'learning_rate': 6.145833333333334e-06, 'epoch': 0.79}

 40%|███▉      | 77/194 [38:06<57:57, 29.72s/it]
 40%|████      | 78/194 [38:36<57:25, 29.70s/it]
                                                
{'loss': 0.3677, 'grad_norm': 3.9349660873413086, 'learning_rate': 6.093750000000001e-06, 'epoch': 0.8}

 40%|████      | 78/194 [38:36<57:25, 29.70s/it]
 41%|████      | 79/194 [39:05<56:57, 29.72s/it]
                                                
{'loss': 0.3458, 'grad_norm': 4.100533485412598, 'learning_rate': 6.041666666666667e-06, 'epoch': 0.81}

 41%|████      | 79/194 [39:05<56:57, 29.72s/it]
 41%|████      | 80/194 [39:35<56:23, 29.68s/it]
                                                
{'loss': 0.3556, 'grad_norm': 3.3279531002044678, 'learning_rate': 5.989583333333334e-06, 'epoch': 0.82}

 41%|████      | 80/194 [39:35<56:23, 29.68s/it]
 42%|████▏     | 81/194 [40:05<55:59, 29.73s/it]
                                                
{'loss': 0.2605, 'grad_norm': 2.665421962738037, 'learning_rate': 5.9375e-06, 'epoch': 0.84}

 42%|████▏     | 81/194 [40:05<55:59, 29.73s/it]
 42%|████▏     | 82/194 [40:35<55:30, 29.74s/it]
                                                
{'loss': 0.4186, 'grad_norm': 3.0744643211364746, 'learning_rate': 5.885416666666667e-06, 'epoch': 0.85}

 42%|████▏     | 82/194 [40:35<55:30, 29.74s/it]
 43%|████▎     | 83/194 [41:04<55:04, 29.77s/it]
                                                
{'loss': 0.3777, 'grad_norm': 3.914085626602173, 'learning_rate': 5.833333333333334e-06, 'epoch': 0.86}

 43%|████▎     | 83/194 [41:04<55:04, 29.77s/it]
 43%|████▎     | 84/194 [41:35<54:47, 29.89s/it]
                                                
{'loss': 0.2795, 'grad_norm': 3.0115182399749756, 'learning_rate': 5.781250000000001e-06, 'epoch': 0.87}

 43%|████▎     | 84/194 [41:35<54:47, 29.89s/it]
 44%|████▍     | 85/194 [42:04<54:14, 29.86s/it]
                                                
{'loss': 0.3959, 'grad_norm': 3.9944891929626465, 'learning_rate': 5.729166666666667e-06, 'epoch': 0.88}

 44%|████▍     | 85/194 [42:04<54:14, 29.86s/it]
 44%|████▍     | 86/194 [42:34<53:38, 29.81s/it]
                                                
{'loss': 0.4021, 'grad_norm': 3.450798511505127, 'learning_rate': 5.677083333333334e-06, 'epoch': 0.89}

 44%|████▍     | 86/194 [42:34<53:38, 29.81s/it]
 45%|████▍     | 87/194 [43:04<53:20, 29.91s/it]
                                                
{'loss': 0.284, 'grad_norm': 3.068922758102417, 'learning_rate': 5.625e-06, 'epoch': 0.9}

 45%|████▍     | 87/194 [43:04<53:20, 29.91s/it]
 45%|████▌     | 88/194 [43:34<52:48, 29.90s/it]
                                                
{'loss': 0.3708, 'grad_norm': 3.1626951694488525, 'learning_rate': 5.572916666666667e-06, 'epoch': 0.91}

 45%|████▌     | 88/194 [43:34<52:48, 29.90s/it]
 46%|████▌     | 89/194 [44:03<52:02, 29.73s/it]
                                                
{'loss': 0.2399, 'grad_norm': 2.434056282043457, 'learning_rate': 5.520833333333334e-06, 'epoch': 0.92}

 46%|████▌     | 89/194 [44:03<52:02, 29.73s/it]
 46%|████▋     | 90/194 [44:33<51:32, 29.73s/it]
                                                
{'loss': 0.3289, 'grad_norm': 3.4771459102630615, 'learning_rate': 5.468750000000001e-06, 'epoch': 0.93}

 46%|████▋     | 90/194 [44:33<51:32, 29.73s/it]
 47%|████▋     | 91/194 [45:03<50:57, 29.69s/it]
                                                
{'loss': 0.2707, 'grad_norm': 3.4767041206359863, 'learning_rate': 5.416666666666667e-06, 'epoch': 0.94}

 47%|████▋     | 91/194 [45:03<50:57, 29.69s/it]
 47%|████▋     | 92/194 [45:33<50:35, 29.76s/it]
                                                
{'loss': 0.3881, 'grad_norm': 3.6288256645202637, 'learning_rate': 5.364583333333334e-06, 'epoch': 0.95}

 47%|████▋     | 92/194 [45:33<50:35, 29.76s/it]
 48%|████▊     | 93/194 [46:03<50:10, 29.81s/it]
                                                
{'loss': 0.3452, 'grad_norm': 3.187014579772949, 'learning_rate': 5.3125e-06, 'epoch': 0.96}

 48%|████▊     | 93/194 [46:03<50:10, 29.81s/it]
 48%|████▊     | 94/194 [46:32<49:44, 29.84s/it]
                                                
{'loss': 0.3306, 'grad_norm': 2.9005351066589355, 'learning_rate': 5.260416666666666e-06, 'epoch': 0.97}

 48%|████▊     | 94/194 [46:32<49:44, 29.84s/it]
 49%|████▉     | 95/194 [47:02<49:15, 29.86s/it]
                                                
{'loss': 0.3223, 'grad_norm': 3.3404111862182617, 'learning_rate': 5.208333333333334e-06, 'epoch': 0.98}

 49%|████▉     | 95/194 [47:02<49:15, 29.86s/it]
 49%|████▉     | 96/194 [47:32<48:43, 29.83s/it]
                                                
{'loss': 0.3087, 'grad_norm': 3.4888906478881836, 'learning_rate': 5.156250000000001e-06, 'epoch': 0.99}

 49%|████▉     | 96/194 [47:32<48:43, 29.83s/it]
 50%|█████     | 97/194 [48:02<48:15, 29.85s/it]
                                                
{'loss': 0.2745, 'grad_norm': 2.8195037841796875, 'learning_rate': 5.104166666666667e-06, 'epoch': 1.0}

 50%|█████     | 97/194 [48:02<48:15, 29.85s/it]